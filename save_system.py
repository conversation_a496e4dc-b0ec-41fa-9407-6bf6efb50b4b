import json
import os
import shutil
from datetime import datetime
import pygame

class SaveSystem:
    def __init__(self):
        self.max_saves = 3
        self.saves_dir = self.get_save_folder()
        self.current_save_slot = None
        self.ensure_saves_directory()

    def get_save_folder(self):
        # %APPDATA%\Xtra Cars\SaveData
        appdata = os.getenv('APPDATA')
        save_folder = os.path.join(appdata, "Xtra Cars", "SaveData")
        return save_folder

    def ensure_saves_directory(self):
        os.makedirs(self.saves_dir, exist_ok=True)
    
    def get_save_slots(self):
        """Get information about all save slots"""
        slots = []
        for i in range(1, self.max_saves + 1):
            slot_path = os.path.join(self.saves_dir, f"slot_{i}")
            if os.path.exists(slot_path):
                # Load save metadata
                try:
                    with open(os.path.join(slot_path, "metadata.json"), 'r') as f:
                        metadata = json.load(f)
                    slots.append({
                        "slot": i,
                        "exists": True,
                        "username": metadata.get("username", "Unknown"),
                        "level": metadata.get("level", 1),
                        "money": metadata.get("money", 0),
                        "save_date": metadata.get("save_date", "Unknown"),
                        "playtime": metadata.get("playtime", "0:00")
                    })
                except:
                    slots.append({
                        "slot": i,
                        "exists": False,
                        "username": "",
                        "level": 0,
                        "money": 0,
                        "save_date": "",
                        "playtime": "0:00"
                    })
            else:
                slots.append({
                    "slot": i,
                    "exists": False,
                    "username": "",
                    "level": 0,
                    "money": 0,
                    "save_date": "",
                    "playtime": "0:00"
                })
        return slots
    
    def save_game(self, slot_number, playtime_seconds=0):
        """Save current game state to specified slot"""
        if slot_number < 1 or slot_number > self.max_saves:
            raise ValueError(f"Invalid slot number: {slot_number}")
        
        slot_path = os.path.join(self.saves_dir, f"slot_{slot_number}")
        
        # Create slot directory if it doesn't exist
        if not os.path.exists(slot_path):
            os.makedirs(slot_path)
        
        try:
            # Load current game data
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)
            
            # Create metadata
            metadata = {
                "username": profile_data.get("username", "Player"),
                "level": profile_data.get("level", {}).get("current", 1),
                "money": profile_data.get("money", 0),
                "save_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "playtime": self.format_playtime(playtime_seconds),
                "version": "1.0"
            }
            
            # Save files to slot
            with open(os.path.join(slot_path, "profile.json"), 'w') as f:
                json.dump(profile_data, f, indent=4)
            
            with open(os.path.join(slot_path, "garage.json"), 'w') as f:
                json.dump(garage_data, f, indent=4)
            
            with open(os.path.join(slot_path, "metadata.json"), 'w') as f:
                json.dump(metadata, f, indent=4)
            
            self.current_save_slot = slot_number
            return True
            
        except Exception as e:
            print(f"Error saving game: {e}")
            return False
    
    def load_game(self, slot_number):
        """Load game state from specified slot"""
        if slot_number < 1 or slot_number > self.max_saves:
            raise ValueError(f"Invalid slot number: {slot_number}")
        
        slot_path = os.path.join(self.saves_dir, f"slot_{slot_number}")
        
        if not os.path.exists(slot_path):
            return False
        
        try:
            # Load saved data
            with open(os.path.join(slot_path, "profile.json"), 'r') as f:
                profile_data = json.load(f)
            
            with open(os.path.join(slot_path, "garage.json"), 'r') as f:
                garage_data = json.load(f)
            
            # Restore to current game files
            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)
            
            with open('data/garage.json', 'w') as f:
                json.dump(garage_data, f, indent=4)
            
            self.current_save_slot = slot_number
            return True
            
        except Exception as e:
            print(f"Error loading game: {e}")
            return False
    
    def delete_save(self, slot_number):
        """Delete save from specified slot"""
        if slot_number < 1 or slot_number > self.max_saves:
            raise ValueError(f"Invalid slot number: {slot_number}")
        
        slot_path = os.path.join(self.saves_dir, f"slot_{slot_number}")
        
        if os.path.exists(slot_path):
            try:
                shutil.rmtree(slot_path)
                if self.current_save_slot == slot_number:
                    self.current_save_slot = None
                return True
            except Exception as e:
                print(f"Error deleting save: {e}")
                return False
        return False
    
    def format_playtime(self, seconds):
        """Format playtime in seconds to HH:MM format"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}:{minutes:02d}"
    
    def create_new_game(self):
        """Create a new game with default values"""
        default_profile = {
            "username": "New Player",
            "money": 2000,
            "level": {
                "current": 1,
                "exp": 0,
                "required_to_next_level": 100
            },
            "race_level": 1,
            "cars": {
                "selected_car": 0,
                "car_colors": {
                    "0": "0"
                }
            },
            "inventory": {
                "owned_cars": ["old"],
                "owned_parts": {
                    "engine": ["Classic V8"],  # Gracz zaczyna z domyślnym silnikiem
                    "turbo": [],
                    "intercooler": [],
                    "ecu": ["Basic ECU"]  # Gracz zaczyna z domyślnym ECU
                }
            },
            "usage_data": {
                "cars": {
                    "0": {
                        "car_age_days": 0,
                        "races_completed": 0,
                        "engine_age_days": 0,
                        "turbo_age_days": 0,
                        "intercooler_age_days": 0,
                        "ecu_age_days": 0,
                        "last_update": 1703001600
                    }
                }
            },
            "fuel_data": {
                "cars": {
                    "0": {
                        "current_fuel": 60,
                        "max_capacity": 60,
                        "last_refuel": 1703001600
                    }
                }
            },
            "tire_data": {
                "cars": {
                    "0": {
                        "tire_type": "standard",
                        "condition": 100.0,
                        "total_distance": 0.0,
                        "last_replacement": 1703001600
                    }
                }
            },
            "maintenance_data": {
                "cars": {
                    "0": {
                        "last_maintenance": 1703001600,
                        "last_maintenance_races": 0,
                        "maintenance_due": False,
                        "total_maintenance_cost": 0,
                        "crashes": 0,
                        "repair_history": []
                    }
                },
                "insurance": None
            }
        }
        
        # Load default garage data - start with "old" car
        try:
            with open('data/shop_data.json', 'r') as f:
                shop_data = json.load(f)
            # Find the "old" car from shop data
            old_car = None
            for car in shop_data[1]['cars']:
                if car['name'] == 'old':
                    old_car = car.copy()
                    break

            if old_car:
                # Set index for garage
                old_car['index'] = 0
                default_garage = [old_car]
            else:
                # Fallback if "old" car not found
                default_garage = [{
                    "index": 0,
                    "name": "old",
                    "weight": 750,
                    "value": 800,
                    "parts": {
                        "engine": {
                            "name": "Basic I4",
                            "horsepower": 120,
                            "weight": 180,
                            "value": 500,
                            "category": "engine"
                        },
                        "turbo": None,
                        "intercooler": None,
                        "ecu": {
                            "name": "Stage 1 Tune",
                            "horsepower_boost_percentage": 10,
                            "weight": 2,
                            "value": 400,
                            "category": "ecu"
                        }
                    }
                }]
        except Exception as e:
            print(f"Error loading shop data for default garage: {e}")
            # Fallback garage with "old" car
            default_garage = [{
                "index": 0,
                "name": "old",
                "weight": 750,
                "value": 800,
                "parts": {
                    "engine": {
                        "name": "Basic I4",
                        "horsepower": 120,
                        "weight": 180,
                        "value": 500,
                        "category": "engine"
                    },
                    "turbo": None,
                    "intercooler": None,
                    "ecu": {
                        "name": "Stage 1 Tune",
                        "horsepower_boost_percentage": 10,
                        "weight": 2,
                        "value": 400,
                        "category": "ecu"
                    }
                }
            }]
        
        # Save new game files
        with open('data/profile.json', 'w') as f:
            json.dump(default_profile, f, indent=4)
        
        with open('data/garage.json', 'w') as f:
            json.dump(default_garage, f, indent=4)
        
        self.current_save_slot = None
        return True

# Global save system instance
save_system = SaveSystem()
